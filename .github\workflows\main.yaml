name: Build and Deploy OneWhispr

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      build_electron:
        description: 'Build Electron App'
        required: false
        default: false
        type: boolean
      deploy_only:
        description: 'Deploy only (use latest artifacts)'
        required: false
        default: false
        type: boolean
      force_python_deploy:
        description: 'Force deploy Python artifacts (runtime + scripts)'
        required: false
        default: false
        type: boolean
      force_scripts_only:
        description: 'Force deploy scripts only (quick update)'
        required: false
        default: false
        type: boolean
      rollback_to_version:
        description: 'Rollback to specific version (e.g., 1.2.1)'
        required: false
        type: string
      emergency_rollback:
        description: 'Emergency rollback to previous version'
        required: false
        default: false
        type: boolean

jobs:
  # Call build workflow if not deploy-only
  build:
    if: github.event.inputs.deploy_only != 'true' && github.event.inputs.rollback_to_version == '' && github.event.inputs.emergency_rollback != 'true'
    uses: ./.github/workflows/build.yaml
    with:
      build_electron: ${{ github.event.inputs.build_electron == 'true' || contains(github.event.head_commit.message, '[build-electron]') }}

  # Call deploy workflow
  deploy:
    needs: [build]
    if: always() && (needs.build.result == 'success' || github.event.inputs.deploy_only == 'true' || github.event.inputs.rollback_to_version != '' || github.event.inputs.emergency_rollback == 'true')
    uses: ./.github/workflows/deploy.yaml
    with:
      version: ${{ needs.build.outputs.version }}
      python-changed: ${{ needs.build.outputs.python-changed }}
      main-whispr-changed: ${{ needs.build.outputs.main-whispr-changed }}
      deps-changed: ${{ needs.build.outputs.deps-changed }}
      use_latest_artifacts: ${{ github.event.inputs.deploy_only == 'true' }}
      force_python_deploy: ${{ github.event.inputs.force_python_deploy == 'true' }}
      force_scripts_only: ${{ github.event.inputs.force_scripts_only == 'true' }}
      rollback_to_version: ${{ github.event.inputs.rollback_to_version }}
      emergency_rollback: ${{ github.event.inputs.emergency_rollback == 'true' }}
    secrets: inherit
