name: Deploy Only (Use Latest Build)

on:
  workflow_dispatch:
    inputs:
      force_python_deploy:
        description: 'Force deploy Python artifacts (runtime + scripts)'
        required: false
        default: false
        type: boolean
      force_scripts_only:
        description: 'Force deploy scripts only (quick update)'
        required: false
        default: false
        type: boolean

jobs:
  deploy-latest:
    runs-on: ubuntu-latest
    
    steps:
      - name: Download latest build artifacts
        uses: dawidd6/action-download-artifact@v3
        with:
          workflow: deploy.yaml
          workflow_conclusion: success
          name: onewhispr-packages
          path: artifacts/
          search_artifacts: true

      - name: Download latest Python artifacts
        uses: dawidd6/action-download-artifact@v3
        with:
          workflow: deploy.yaml
          workflow_conclusion: success
          name: onewhispr-python-builds
          path: artifacts/python-builds/
          search_artifacts: true
          if_no_artifact_found: ignore

      - name: Get version from artifacts
        id: get-version
        run: |
          # Try to extract version from setup file or use timestamp
          if [ -f "artifacts/OneWhisprSetup.exe" ]; then
            # Use current timestamp as version for deploy-only
            VERSION=$(date +%Y.%m.%d.%H%M)
            echo "version=$VERSION" >> $GITHUB_OUTPUT
            echo "Version for deployment: $VERSION"
          else
            echo "No setup file found in artifacts"
            ls -la artifacts/
            exit 1
          fi

      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}

      - name: Prepare deployment structure
        run: |
          VERSION="${{ steps.get-version.outputs.version }}"
          echo "Deploying version: $VERSION"

          # Create directory structure for setup installer
          mkdir -p "updates/setup/versions/$VERSION"
          cp -r artifacts/*.exe artifacts/*.nupkg artifacts/RELEASES "updates/setup/versions/$VERSION/" 2>/dev/null || true

          # Copy Microsoft Store package if it exists
          if ls artifacts/*.appx 1> /dev/null 2>&1; then
            cp artifacts/*.appx "updates/setup/versions/$VERSION/"
            echo "Microsoft Store package copied to versions/$VERSION"
          fi

          # Create latest setup directory
          mkdir -p "updates/setup/latest"
          cp -r artifacts/*.exe artifacts/*.nupkg artifacts/RELEASES "updates/setup/latest/" 2>/dev/null || true

          # Copy Microsoft Store package to latest if it exists
          if ls artifacts/*.appx 1> /dev/null 2>&1; then
            cp artifacts/*.appx "updates/setup/latest/"
            echo "Microsoft Store package copied to latest"
          fi

          # Handle Python deployment based on inputs
          if [ "${{ github.event.inputs.force_python_deploy }}" = "true" ]; then
            echo "Force deploying Python runtime and scripts"

            # Deploy compressed base runtime (~1.3GB)
            mkdir -p "updates/backend-runtime/versions/$VERSION"
            cp "artifacts/python-builds/OneWhispr-Runtime-Base.7z" "updates/backend-runtime/versions/$VERSION/" 2>/dev/null || echo "No runtime base found"

            # Create latest runtime directory
            mkdir -p "updates/backend-runtime/latest"
            cp "artifacts/python-builds/OneWhispr-Runtime-Base.7z" "updates/backend-runtime/latest/" 2>/dev/null || echo "No runtime base found"

            # Deploy scripts bytecode (~200KB)
            mkdir -p "updates/backend-scripts/versions/$VERSION"
            cp "artifacts/python-builds/OneWhispr-Scripts.7z" "updates/backend-scripts/versions/$VERSION/" 2>/dev/null || echo "No scripts found"

            # Create latest scripts directory
            mkdir -p "updates/backend-scripts/latest"
            cp "artifacts/python-builds/OneWhispr-Scripts.7z" "updates/backend-scripts/latest/" 2>/dev/null || echo "No scripts found"

            # Mark that both runtime and scripts were deployed
            touch "updates/RUNTIME_DEPLOYED"
            touch "updates/SCRIPTS_DEPLOYED"

          elif [ "${{ github.event.inputs.force_scripts_only }}" = "true" ]; then
            echo "Force deploying scripts only (quick update)"

            # Deploy updated scripts package (~200KB quick update)
            mkdir -p "updates/backend-scripts/versions/$VERSION"
            cp "artifacts/python-builds/OneWhispr-Scripts.7z" "updates/backend-scripts/versions/$VERSION/" 2>/dev/null || echo "No scripts found"

            # Create latest scripts directory
            mkdir -p "updates/backend-scripts/latest"
            cp "artifacts/python-builds/OneWhispr-Scripts.7z" "updates/backend-scripts/latest/" 2>/dev/null || echo "No scripts found"

            # Mark that only scripts were deployed
            touch "updates/SCRIPTS_DEPLOYED"
          fi

          # Always deploy main app
          mkdir -p "updates/main-app/versions/$VERSION"
          cp -r artifacts/win-unpacked/* "updates/main-app/versions/$VERSION/"

          mkdir -p "updates/main-app/latest"
          cp -r artifacts/win-unpacked/* "updates/main-app/latest/"

          # Create JSON manifest for the setup app to know what to download
          cd "updates/main-app/versions/$VERSION"

          # Get all files and create JSON manifest
          find . -type f -exec bash -c '
            file="$1"
            relativePath="${file#./}"
            size=$(stat -c%s "$file")
            checksum=$(sha256sum "$file" | cut -d" " -f1)
            baseUrl="https://whispr.one/updates/main-app/latest"
            
            echo "  {\"path\": \"./$relativePath\", \"size\": $size, \"checksum\": \"$checksum\", \"url\": \"$baseUrl/$relativePath\"}"
          ' _ {} \; > /tmp/files.json

          # Create manifest
          cat > "../../../manifest-$VERSION.json" << EOF
          {
            "version": "$VERSION",
            "files": [
          $(cat /tmp/files.json | sed '$!s/$/,/')
            ],
            "totalSize": $(find . -type f -exec stat -c%s {} \; | awk '{sum+=$1} END {print sum}')
          }
          EOF

          cd ../../../../
          cp "updates/manifest-$VERSION.json" "updates/main-app/latest/manifest.json"
        shell: bash

      - name: Create updates directory and transfer files to VPS
        run: |
          # Create updates directory on VPS (in nginx web root)
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "mkdir -p /var/www/html/updates"
          
          # Use scp for reliable transfer (more compatible on Windows than rsync)
          scp -r -o StrictHostKeyChecking=no updates/* ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:/var/www/html/updates/
        shell: bash

      - name: Update version info and cleanup old versions
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} << 'ENDSSH'
            cd /var/www/html/updates
            
            # Extract version from the uploaded files
            VERSION=$(ls setup/versions/ | sort -V | tail -1)
            
            # Create version.json for setup installer
            cat > setup/version.json << EOF
            {
              "version": "$VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "downloadUrl": "https://whispr.one/updates/setup/latest/OneWhisprSetup.exe",
              "releaseNotes": "Deploy-only update using latest build artifacts",
              "deltaUpdatesUrl": "https://whispr.one/updates/setup/versions/$VERSION/"
            }
            EOF

            # Create latest.yml for electron-updater
            SETUP_FILE_SIZE=$(stat -c%s setup/latest/OneWhisprSetup.exe)
            SETUP_SHA512=$(sha512sum setup/latest/OneWhisprSetup.exe | cut -d' ' -f1)
            cat > setup/latest.yml << EOF
            version: $VERSION
            files:
              - url: OneWhisprSetup.exe
                sha512: $SETUP_SHA512
                size: $SETUP_FILE_SIZE
            path: OneWhisprSetup.exe
            sha512: $SETUP_SHA512
            releaseDate: $(date -u +%Y-%m-%dT%H:%M:%SZ)
            EOF
            
            # Create version.json for main app updates
            cat > main-app/version.json << EOF
            {
              "version": "$VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "releaseNotes": "Deploy-only update using latest build artifacts",
              "downloadUrl": "https://whispr.one/updates/main-app/latest/",
              "manifestUrl": "https://whispr.one/updates/main-app/latest/manifest.json",
              "versionsUrl": "https://whispr.one/updates/main-app/versions/$VERSION/"
            }
            EOF

            # Handle backend version files based on what was deployed
            if [ -f "RUNTIME_DEPLOYED" ]; then
              echo "Creating backend runtime version file..."
              RUNTIME_CHECKSUM=$(sha256sum backend-runtime/latest/OneWhispr-Runtime-Base.7z | cut -d' ' -f1)
              cat > backend-runtime/runtime-version.json << EOF
            {
              "version": "$VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "releaseNotes": "Deploy-only runtime update",
              "downloadUrl": "https://whispr.one/updates/backend-runtime/latest/OneWhispr-Runtime-Base.7z",
              "versionsUrl": "https://whispr.one/updates/backend-runtime/versions/$VERSION/",
              "compressionType": "7z-lzma2-ultra",
              "checksum": "$RUNTIME_CHECKSUM"
            }
            EOF
              rm -f "RUNTIME_DEPLOYED"
            fi

            if [ -f "SCRIPTS_DEPLOYED" ]; then
              echo "Creating backend scripts version file..."
              SCRIPTS_CHECKSUM=$(sha256sum backend-scripts/latest/OneWhispr-Scripts.7z | cut -d' ' -f1)
              cat > backend-scripts/scripts-version.json << EOF
            {
              "version": "$VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "releaseNotes": "Deploy-only scripts update",
              "downloadUrl": "https://whispr.one/updates/backend-scripts/latest/OneWhispr-Scripts.7z",
              "versionsUrl": "https://whispr.one/updates/backend-scripts/versions/$VERSION/",
              "updateType": "bytecode",
              "compressionType": "7z",
              "checksum": "$SCRIPTS_CHECKSUM"
            }
            EOF
              rm -f "SCRIPTS_DEPLOYED"
            fi
            
            echo "Deploy-only update completed successfully!"
            echo "Deployed version: $VERSION"
          ENDSSH
        shell: bash
